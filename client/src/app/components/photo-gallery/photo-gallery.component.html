<div class="gallery">
  <div class="card" *ngFor="let photo of images" (click)="onPhotoClick.emit(photo)">
    <img *ngIf="photo.name" [src]="getBackgroundImageUrl(photo.name)" width="297" height="auto"
      [alt]="photo?.originalName" />
    <div class="photo-item-info">
      <div class="photo-item-actions {{customPhotoActionsClasses}}">
        @if(actionsSettings.viewIcon) {
          <svg (click)="actionsSettings.viewIcon.action(photo);$event.stopPropagation()" width="20" height="20"
            viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1.40915 13.6386C1.73558 13.2422 2.03285 12.8867 2.3243 12.5312C2.67404 12.1115 3.01795 11.6919 3.36769 11.2722C4.39942 10.0424 6.22971 10.0249 7.29058 11.2314C7.59952 11.5811 7.88514 11.9483 8.19408 12.3213C8.57879 11.8784 8.95767 11.4471 9.33655 11.0158C9.82036 10.4621 10.2983 9.90836 10.7821 9.35466C11.8547 8.13069 13.6966 8.11903 14.7808 9.34301C15.9583 10.6719 17.1182 12.0124 18.284 13.3471C18.3773 13.4521 18.4647 13.5511 18.6046 13.7085C18.6046 13.5745 18.6046 13.5045 18.6046 13.4287C18.6046 12.3213 18.6046 11.2198 18.6104 10.1124C18.6104 9.97248 18.6338 9.82094 18.6921 9.69271C18.8203 9.40712 19.1467 9.24975 19.4323 9.31386C19.7529 9.3838 19.9861 9.65191 19.9919 9.97831C19.9977 10.4504 19.9977 10.9225 19.9977 11.3946C19.9977 12.776 19.9977 14.1573 19.9977 15.5386C19.9919 17.8467 18.3656 19.6885 16.0748 19.9741C15.8825 19.9974 15.6843 19.9974 15.4861 19.9974C11.8314 19.9974 8.17076 20.0032 4.51599 19.9974C2.37093 19.9974 0.616414 18.651 0.12678 16.6169C0.0451745 16.273 0.00437173 15.9117 0.00437173 15.562C-0.00145724 11.8726 -0.00145724 8.17731 0.00437173 4.48791C0.00437173 2.28475 1.44413 0.512904 3.58336 0.0815983C3.86898 0.0233138 4.16043 0.00582845 4.45188 0.00582845C6.58528 0 8.71868 0 10.8579 0C11.3009 0 11.5982 0.250623 11.6215 0.635301C11.6448 1.01415 11.4117 1.31723 11.0328 1.37551C10.9395 1.393 10.8463 1.393 10.753 1.393C8.66039 1.393 6.56196 1.38717 4.46936 1.393C2.91303 1.40466 1.7006 2.42464 1.43247 3.95752C1.3975 4.14403 1.3975 4.33637 1.3975 4.52288C1.3975 7.47207 1.3975 10.4271 1.3975 13.3763C1.3975 13.4462 1.40332 13.5162 1.40915 13.6386ZM9.9894 18.6102V18.6044C11.9013 18.6044 13.8132 18.6277 15.7251 18.5986C17.1415 18.5753 18.3831 17.3688 18.5172 15.9583C18.5288 15.8592 18.4822 15.731 18.4181 15.6552C17.9518 15.1073 17.4738 14.5711 16.9958 14.0291C15.9058 12.7876 14.8158 11.552 13.7316 10.3105C13.4693 10.0074 13.1545 9.82677 12.7465 9.83259C12.356 9.83259 12.047 9.99579 11.7964 10.2872C10.7705 11.4646 9.74458 12.6477 8.71868 13.8251C8.36312 14.2272 7.90846 14.2214 7.56455 13.8076C7.14486 13.3063 6.72517 12.8051 6.31132 12.298C5.73425 11.5986 4.89488 11.5986 4.32364 12.298C3.43181 13.3879 2.53997 14.4837 1.63065 15.562C1.44413 15.7834 1.40915 15.9874 1.47327 16.2497C1.79969 17.6602 3.00046 18.6161 4.47519 18.6219C6.31132 18.6102 8.14744 18.6102 9.9894 18.6102Z"
              fill="white" />
            <path
              d="M18.4799 5.4438C18.8996 5.84596 19.331 6.24813 19.739 6.66778C19.8556 6.78435 19.9488 6.95337 19.9838 7.11074C20.0479 7.41965 19.8847 7.69358 19.6166 7.83347C19.3426 7.96752 19.0453 7.92672 18.8122 7.69941C18.4216 7.32056 18.0311 6.93588 17.6522 6.54538C17.5415 6.42881 17.4657 6.42298 17.3258 6.50458C16.0959 7.17485 14.866 7.16319 13.6885 6.41132C12.546 5.67111 12.0156 4.57536 12.103 3.21733C12.2138 1.43966 13.7118 0.0466576 15.5188 0.00585846C17.2966 -0.0349407 18.8238 1.29395 19.0395 3.04831C19.1444 3.89343 18.9521 4.67445 18.4799 5.4438ZM15.6062 1.39886C14.4579 1.3872 13.5078 2.3081 13.4962 3.44464C13.4845 4.61616 14.3996 5.5662 15.5713 5.58368C16.7137 5.60117 17.6755 4.65113 17.6872 3.50876C17.6872 2.3489 16.7662 1.41051 15.6062 1.39886Z"
              fill="white" />
            <path
              d="M2.79707 5.36801C2.77959 3.96336 3.91624 2.80349 5.32102 2.79184C6.73746 2.78018 7.89159 3.90507 7.90908 5.30973C7.92074 6.73187 6.78992 7.89756 5.37931 7.90922C3.95704 7.91504 2.80873 6.79015 2.79707 5.36801ZM5.36182 4.19067C4.70898 4.18484 4.19603 4.69191 4.1902 5.33887C4.18437 5.98583 4.69732 6.51039 5.34433 6.51039C5.98552 6.51039 6.51013 5.98583 6.51013 5.3447C6.51013 4.7094 5.99718 4.19067 5.36182 4.19067Z"
              fill="white" />
          </svg>
        }
        @if(actionsSettings.favoritIcon) {
          <div (click)="actionsSettings.favoritIcon.action(photo);$event.stopPropagation()">
            @if(!inFavourites(photo.id)) {
            <svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M11.5791 0.00181344C12.0842 0.025927 12.5655 0.26574 12.9023 0.673688C13.0948 0.937705 13.2625 1.22635 13.3828 1.56236C13.8399 2.97845 14.2977 4.39432 14.7548 5.81041L15.1396 6.98619H20.8662C21.203 6.98619 21.5159 7.03472 21.8047 7.13072C22.3339 7.32271 22.7426 7.73072 22.9111 8.25865C23.0795 8.78671 23.008 9.33914 22.6953 9.8192C22.5268 10.0832 22.3096 10.299 22.0449 10.4911C20.4811 11.6191 18.9177 12.7718 17.3779 13.8758C17.9794 15.6999 18.5808 17.5 19.1582 19.3241C19.3747 19.9722 19.3506 20.5251 19.0859 21.0292C18.7008 21.797 17.8101 22.1809 16.9922 21.9169C16.6795 21.8208 16.3905 21.6772 16.1259 21.4852C14.8507 20.5731 13.5756 19.6362 12.2763 18.7001L11.5068 18.1483L10.5918 18.8202C9.36478 19.7082 8.13808 20.6206 6.9111 21.5086C6.40582 21.7967 5.94833 21.9647 5.49117 21.9647H5.41891C4.93771 21.9407 4.50478 21.7246 4.16793 21.3885C3.83117 21.0046 3.66289 20.5248 3.7109 19.9969C3.73496 19.7809 3.78326 19.5407 3.85543 19.3006C4.33665 17.7885 4.98606 15.8441 5.63571 13.8758C4.07174 12.7477 2.50729 11.6198 0.943323 10.4676C0.750833 10.3236 0.149567 9.89102 0.0292607 9.12291C-0.0669423 8.57097 0.0773366 8.04306 0.414026 7.65904C0.774946 7.22698 1.32882 6.98619 1.97848 6.98619H7.87301C7.99326 6.65026 8.08966 6.3143 8.20992 5.97838C8.35428 5.54635 8.47475 5.13795 8.6191 4.70591C8.95592 3.67389 9.26897 2.61782 9.62985 1.5858C9.75016 1.20179 9.96677 0.841579 10.2314 0.553571C10.5683 0.193632 11.0739 -0.0221858 11.5791 0.00181344ZM11.4824 1.39341C11.4343 1.39341 11.3378 1.3941 11.2656 1.49009C11.1454 1.61011 11.0496 1.80213 10.9775 1.994C10.6406 3.02615 10.3036 4.08254 9.96676 5.09068C9.82246 5.52254 9.70187 5.93031 9.55758 6.36216C9.43731 6.69811 9.34092 7.05843 9.22067 7.39439L8.90817 8.37877H1.97848C1.76215 8.37877 1.59376 8.45087 1.52145 8.54674C1.44926 8.61875 1.42512 8.73922 1.44918 8.85924C1.47327 8.95519 1.52175 9.07565 1.7861 9.26744C3.422 10.4434 5.03398 11.6432 6.66989 12.8192C7.07893 13.1312 7.22343 13.5637 7.07907 14.0438C6.40537 16.084 5.7318 18.1487 5.22653 19.7088C5.17853 19.8287 5.15431 19.9723 5.15426 20.0682C5.15426 20.2122 5.1788 20.3082 5.25094 20.4042C5.29899 20.4761 5.39461 20.5008 5.46676 20.5008C5.58706 20.5008 5.73176 20.4766 6.02047 20.2606C7.2476 19.3725 8.47499 18.4603 9.70211 17.5721L11.4824 16.2762L13.1181 17.452C14.3933 18.3881 15.6691 19.3001 16.9443 20.2362C17.0887 20.3322 17.2329 20.405 17.4013 20.453C17.5456 20.501 17.6904 20.4289 17.7627 20.285C17.8108 20.189 17.8586 20.0205 17.7382 19.6844C17.1127 17.7882 16.4869 15.8921 15.8613 13.996C15.6929 13.4919 15.838 13.0834 16.247 12.7713C17.859 11.6193 19.4954 10.4191 21.1074 9.24302C21.2276 9.14708 21.3237 9.05046 21.4199 8.93052C21.4919 8.83458 21.5158 8.7149 21.4677 8.619C21.4437 8.52299 21.3472 8.45119 21.2509 8.40318C21.1306 8.35523 20.9622 8.33092 20.7939 8.33092H14.0566L13.8886 7.87486C13.8646 7.8029 13.8404 7.75487 13.8164 7.70689L13.3349 6.21861C12.8778 4.80241 12.421 3.38578 11.9638 1.96959C11.9157 1.80166 11.8188 1.63348 11.7226 1.51353C11.6504 1.41787 11.5304 1.39341 11.4824 1.39341Z"
                fill="white" />
            </svg>
            } @else {
            <svg width="23" height="22" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M5.96868 23.9618C5.94253 23.9618 5.91637 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.084123 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25538 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60569 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43373 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5315C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618Z"
                fill="#fff" />
            </svg>
            }
          </div>
        }
        @if(actionsSettings.shareIcon) {
          <svg (click)="actionsSettings.shareIcon.action(photo);$event.stopPropagation()" width="20" height="20"
            viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M13.2698 5.84174C11.2555 6.84951 9.24711 7.85131 7.22087 8.86503C7.44733 9.61042 7.45329 10.3558 7.22087 11.1131C9.23519 12.1209 11.2436 13.1287 13.2281 14.1185C13.538 13.8323 13.8121 13.5222 14.1339 13.2897C16.3628 11.6617 19.5452 13.0094 19.9445 15.7465C20.2901 18.1257 18.3473 20.189 15.9456 19.9862C14.2412 19.8431 12.8109 18.4597 12.6262 16.7542C12.5904 16.4024 12.5964 16.0386 12.644 15.6928C12.6738 15.49 12.6202 15.4244 12.4593 15.3469C10.731 14.4882 9.00873 13.6236 7.28046 12.7589C7.0838 12.6576 6.88118 12.5681 6.68451 12.4548C6.56532 12.3892 6.49977 12.4071 6.40441 12.5085C5.63563 13.3195 4.69403 13.713 3.57364 13.7011C1.84537 13.6773 0.272056 12.2879 0.0396338 10.5705C-0.240464 8.54899 0.987199 6.77199 2.9896 6.36649C4.3007 6.10412 5.43897 6.49768 6.38058 7.45178C6.50573 7.577 6.5832 7.58893 6.73219 7.51141C8.6452 6.54539 10.5642 5.59129 12.4831 4.6372C12.6321 4.56564 12.6798 4.50004 12.644 4.32115C12.2746 2.29966 13.7048 0.325875 15.7311 0.0396458C17.8527 -0.264472 19.7597 1.21438 19.9802 3.33128C20.1888 5.31103 18.7228 7.1417 16.7442 7.36233C15.3497 7.52334 14.2293 7.01647 13.3532 5.93715C13.3234 5.9133 13.2996 5.88348 13.2698 5.84174ZM5.95149 9.99802C5.95149 8.75173 4.92645 7.72012 3.68687 7.72608C2.45324 7.73205 1.43416 8.74577 1.42225 9.98013C1.41033 11.2145 2.43537 12.2521 3.68091 12.264C4.92049 12.2759 5.95149 11.2503 5.95149 9.99802ZM14.0267 16.2951C14.0207 17.5413 15.0457 18.573 16.2853 18.567C17.5189 18.567 18.5559 17.5294 18.5559 16.2951C18.5499 15.0607 17.5428 14.0529 16.2972 14.041C15.0517 14.0231 14.0326 15.0368 14.0267 16.2951ZM16.2853 5.95504C17.5309 5.95504 18.5559 4.92939 18.5559 3.68906C18.5499 2.44874 17.513 1.41712 16.2794 1.42308C15.0398 1.42905 14.0386 2.44278 14.0267 3.6831C14.0207 4.93535 15.0338 5.95504 16.2853 5.95504Z"
              fill="white" />
          </svg>
        }
        @if(actionsSettings.deleteIcon) {
          <svg (click)="actionsSettings.deleteIcon.action(photo);$event.stopPropagation()" width="18" height="22"
            viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M10.6234 0C10.7094 0.0286963 10.7955 0.0573925 10.8816 0.0803496C11.7655 0.338616 12.3624 1.11342 12.3796 2.0317C12.3853 2.26127 12.3796 2.49084 12.3796 2.7491C12.4657 2.7491 12.5403 2.7491 12.6149 2.7491C13.7685 2.7491 14.9221 2.7491 16.0699 2.7491C17.1432 2.7491 17.872 3.48373 17.872 4.55697C17.872 5.29159 17.872 6.03196 17.872 6.76658C17.872 7.28885 17.608 7.55286 17.0743 7.5586C16.9882 7.5586 16.9078 7.5586 16.8103 7.5586C16.7758 8.24731 16.7471 8.90732 16.7127 9.57308C16.6037 11.863 16.4946 14.1473 16.3856 16.4372C16.3282 17.6654 16.2823 18.8994 16.2019 20.1276C16.133 21.1664 15.2607 21.9699 14.2219 21.9928C13.7398 22.0043 13.2634 21.9986 12.7813 21.9986C9.79117 21.9986 6.79528 21.9986 3.80513 21.9986C2.72041 21.9986 1.85952 21.3213 1.69308 20.2997C1.62421 19.8578 1.62421 19.3987 1.60125 18.951C1.49221 16.7012 1.3889 14.4457 1.28559 12.1959C1.21672 10.6865 1.14211 9.17707 1.0675 7.66764C1.0675 7.63895 1.05602 7.61599 1.05028 7.56434C0.92402 7.56434 0.809235 7.57008 0.688711 7.56434C0.298441 7.55286 0.00573925 7.27164 0 6.88137C0.00573925 6.04917 0 5.21698 0.0172178 4.39053C0.0344355 3.48947 0.792017 2.76058 1.7103 2.75484C2.89258 2.74336 4.07487 2.75484 5.25142 2.75484C5.32603 2.75484 5.40638 2.75484 5.50968 2.75484C5.50968 2.54249 5.50968 2.34162 5.50968 2.14648C5.51542 1.06176 6.14674 0.275484 7.19703 0.0344355C7.22572 0.0286963 7.24868 0.0114785 7.27164 0C8.39079 0 9.50421 0 10.6234 0ZM2.47362 7.57008C2.47362 7.77095 2.46788 7.94887 2.47362 8.12679C2.58266 10.4225 2.69171 12.7182 2.80076 15.0082C2.87537 16.6151 2.94998 18.2279 3.03033 19.8349C3.05902 20.3858 3.31155 20.6154 3.86252 20.6154C7.24868 20.6154 10.6348 20.6154 14.021 20.6154C14.5834 20.6154 14.8302 20.3858 14.8589 19.8176C14.9508 17.8893 15.0426 15.9551 15.1344 14.0267C15.2148 12.3624 15.2951 10.698 15.3755 9.02785C15.3984 8.54575 15.4214 8.05791 15.4386 7.56434C11.1055 7.57008 6.79528 7.57008 2.47362 7.57008ZM16.5061 6.18118C16.5061 5.65891 16.5061 5.15959 16.5061 4.66027C16.5061 4.21835 16.42 4.13226 15.9838 4.13226C11.2891 4.13226 6.5944 4.13226 1.89969 4.13226C1.85952 4.13226 1.8136 4.13226 1.77343 4.13226C1.5209 4.138 1.3889 4.26427 1.3889 4.51679C1.38316 5.02759 1.3889 5.53264 1.3889 6.04344C1.3889 6.08935 1.40038 6.13526 1.40612 6.18692C6.43944 6.18118 11.4613 6.18118 16.5061 6.18118ZM11.0079 2.74336C11.0079 2.4851 11.0194 2.24979 11.0079 2.01448C10.9907 1.66438 10.7037 1.38316 10.3479 1.37742C9.41812 1.37168 8.48836 1.37168 7.5586 1.37742C7.23146 1.37742 6.93876 1.62995 6.90432 1.95135C6.87563 2.20961 6.89858 2.47362 6.89858 2.74336C8.27027 2.74336 9.62473 2.74336 11.0079 2.74336Z"
              fill="white" />
            <path
              d="M6.20236 14.1013C6.20236 15.5763 6.20236 17.0513 6.20236 18.5263C6.20236 19.0371 5.73748 19.3757 5.28408 19.2035C5.01433 19.1002 4.83642 18.8592 4.83068 18.5607C4.82494 18.2451 4.83068 17.9294 4.83068 17.6138C4.83068 14.9737 4.83068 12.3279 4.83068 9.68784C4.83068 9.14262 5.28982 8.79826 5.76043 8.98192C6.0474 9.09096 6.20236 9.34349 6.20236 9.69932C6.20236 11.1628 6.20236 12.6321 6.20236 14.1013Z"
              fill="white" />
            <path
              d="M9.63986 14.0841C9.63986 15.5591 9.63986 17.0341 9.63986 18.5091C9.63986 19.0371 9.17498 19.3815 8.71584 19.2036C8.44035 19.1003 8.26818 18.8535 8.26818 18.5378C8.26244 17.9811 8.26818 17.4187 8.26818 16.8619C8.26818 14.4687 8.26818 12.0811 8.26818 9.68788C8.26818 9.14839 8.72732 8.80403 9.20367 8.98769C9.47916 9.09674 9.63986 9.34352 9.63986 9.68214C9.63986 11.0136 9.63986 12.3452 9.63986 13.6767C9.63986 13.8087 9.63986 13.9464 9.63986 14.0841Z"
              fill="white" />
            <path
              d="M13.0674 14.1128C13.0674 15.5821 13.0674 17.0456 13.0674 18.5148C13.0674 18.8247 12.9354 19.0543 12.6542 19.1863C12.3844 19.3068 12.1376 19.2552 11.9195 19.06C11.7474 18.9051 11.69 18.7099 11.6957 18.4804C11.7014 17.2866 11.6957 16.0871 11.6957 14.8933C11.6957 13.1543 11.6957 11.4154 11.6957 9.67636C11.6957 9.13687 12.1721 8.79825 12.6369 8.98765C12.9124 9.09669 13.0674 9.34922 13.0674 9.69358C13.0731 11.1628 13.0674 12.6378 13.0674 14.1128Z"
              fill="white" />
          </svg>
        }
        @if(actionsSettings.likeIcon) {
          <div (click)="actionsSettings.likeIcon.action(photo);$event.stopPropagation()">
            @if(!photo.liked) {
              <svg width="20" height="19"
                viewBox="0 0 20 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M9.79446 2.0317C9.64666 1.89924 9.51023 1.7728 9.3738 1.64035C8.37329 0.646931 7.42395 0.141191 6.39503 0.0508802C6.20744 0.032818 6.01984 0.0267973 5.83793 0.0267973C4.06431 0.0267973 2.59198 0.785407 1.438 2.29661C0.23853 3.86801 -0.210559 5.74648 0.0907287 7.88383C0.33517 9.61178 1.04575 11.0387 2.19974 12.1224C3.9563 13.7661 5.76403 15.4097 7.51491 16.9932C8.16296 17.5832 8.81101 18.1672 9.45907 18.7572C9.55571 18.8476 9.65803 18.9138 9.75467 18.9499C10.0332 19.0583 10.289 18.992 10.5562 18.7392C11.551 17.8301 12.5458 16.9209 13.5463 16.0118C14.2285 15.3856 14.9164 14.7655 15.5985 14.1394C15.8373 13.9226 16.0817 13.7119 16.3148 13.5012C16.8548 13.0255 17.4119 12.5378 17.9122 12.002C19.6858 10.0874 20.3338 7.79954 19.8393 5.21063C19.4186 3.01909 18.293 1.46575 16.4967 0.586724C14.7742 -0.256176 13.1427 -0.189948 11.659 0.779387C11.1019 1.14063 10.5846 1.62229 10.1185 2.2063L10.0616 2.27854L9.99911 2.21834C9.93089 2.15211 9.86268 2.0919 9.79446 2.0317ZM10.6301 3.86801C10.6813 3.78974 10.7324 3.71147 10.7836 3.63321C10.9655 3.35023 11.1531 3.06124 11.3805 2.81439C12.6084 1.45973 14.5355 1.20084 16.1727 2.18221C17.3835 2.9047 18.1282 4.05466 18.3954 5.59596C18.7762 7.82964 18.1396 9.73219 16.491 11.2434C15.0187 12.592 13.5179 13.9648 12.0626 15.2833C11.443 15.8492 10.8234 16.4092 10.2094 16.9751C10.181 17.0052 10.1469 17.0293 10.1128 17.0594L9.99911 17.1557L6.67926 14.1273C6.32681 13.8022 5.96299 13.4771 5.61054 13.164C4.83174 12.4656 4.02452 11.7431 3.25709 10.9965C2.14858 9.91883 1.51758 8.44376 1.48916 6.84827C1.46073 5.33707 1.97804 3.89812 2.91601 2.9047C4.18369 1.56208 6.19607 1.20686 7.69682 2.05578C8.28802 2.39294 8.78828 2.89868 9.21463 3.6031C9.23736 3.63923 9.2601 3.67535 9.27716 3.71147C9.30558 3.76566 9.334 3.81383 9.36811 3.86199C9.51591 4.09078 9.7433 4.22926 9.98774 4.22926H9.99343C10.2436 4.23528 10.4766 4.10282 10.6301 3.86801Z"
                  fill="white" />
              </svg>
            } @else {
              <svg width="20" height="19" viewBox="0 0 24 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.623 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249Z"
                  fill="#fff" />
              </svg>
            }
          </div>
        }
      </div>

      <div class="photo-item-description">
        {{photo.description}}
      </div>
    </div>
  </div>
</div>