import {Component, ElementRef, inject, ViewChild} from "@angular/core";
import {CommonModule, NgOptimizedImage} from "@angular/common";
import {environment} from "@/env/environment";
import {ToasterService} from "@/services/toaster.service";
import {Router} from "@angular/router";
import {LibraryItemComponent} from "@/components/library-item/library-item.component";
import {LibraryService} from "@/services/library.service";

@Component({
  selector: 'LibraryFavourites',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage, LibraryItemComponent],
  templateUrl: './library-favourites.component.html',
  styleUrl: '../favourites.component.scss'
})
export class LibraryFavouritesComponent {
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  toasterService = inject(ToasterService);
  libraryService = inject(LibraryService);
  router = inject(Router);
  message: string = "";

  items: any = []
  page: number = 1;
  totalPages = 1;

  ngOnInit() {
    this.get()
  }

  get() {
    this.libraryService
      .getFavourites(true, this.page)
      .subscribe((res: any) => {
        this.totalPages = res.pagination.totalPages;
        this.items = [...this.items, ...res.items]
      })
  }

  getUrl(item: any) {
    if(item && item.name) {
      const pathMatch = item.name.match(/^(photo\/[^\/]+)/);
      let url = '';

      if (pathMatch && pathMatch[1]) {
        const photoPath = pathMatch[1];
        url = this.environment.baseUrl + '/ru/' + photoPath;
      } else {
        url = this.environment.baseUrl + '/ru/photo/' + item.slug;
      }
      return url;
    }
    return ''
  }

  copyUrl(item: any)  {
    if (item && item.name) {
      const url = this.getUrl(item)

      navigator.clipboard.writeText(url)
        .then(() => {
          this.toasterService.showToast('Ссылка скопирована в буфер обмена!', 'success', 'bottom-middle', 3000);
        })
        .catch(err => {
          console.error('Не удалось скопировать ссылку: ', err);
          this.toasterService.showToast('Не удалось скопировать ссылку', 'error', 'bottom-middle', 3000);
        });
    }
  }

  redirect(item: any) {
    const url = this.getUrl(item);
    this.router.navigateByUrl(url.replace(this.environment.baseUrl, ''));
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  delete(item: any) {
    const index = this.items.findIndex((e: any) => e.id === item.id);
    if (index !== -1) {
      this.items.splice(index, 1);
    }
  }

  protected readonly environment = environment;
}
