.gallery {
  column-count: 3;
  column-gap: 16px;
  max-width: 931px;
  margin: 0 auto;

  .card {
    display: inline-block;
    width: 100%;
    margin-bottom: 11px;
    break-inside: avoid;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
    cursor: pointer;

    img {
      width: 100%;
      height: auto;
      display: block;
      border-radius: 20px;
      min-height: 165px;
      object-fit: cover;
    }

    .photo-item-info {
      background: rgba(83, 46, 0, 0.63);
      padding: 25px 30px;
      width: 100%;
      height: 100%;
      min-height: fit-content;
      // max-height: 290px;
      position: absolute;
      bottom: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 14px;
      border-radius: 0 0 20px 20px;
      font-family: Prata;
      font-weight: 400;
      font-size: 18px;
      line-height: 24px;
      letter-spacing: 0;
      color: #fff;
      transform: translateY(101%);
      transition: transform 200ms ease-in-out;

      .photo-item-actions {
        display: flex;
        gap: 40px;
        align-items: center;
        justify-content: flex-end;
      }

      .photo-item-description {
        color: #fff;
        font-family: Prata;
        font-weight: 400;
        font-style: Regular;
        font-size: 15px;
        leading-trim: NONE;
        line-height: 20px;
        letter-spacing: 0;
        text-align: center;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    &:hover {
      .photo-item-info {
        transform: translateY(0);
      }
    }
  }
}

svg.in-favourites {
  path {
    stroke: #fff;
  }
}

@media (max-width: 1024px) {
  .gallery {
    column-count: 2;
    column-gap: 12px;
    max-width: 579px;

    .card {
      margin-bottom: 7px;
      border-radius: 15px;

      img {
        border-radius: 15px;
        min-height: 145px;

      }

      .photo-item-info {
        padding: 16px 12px;
        border-radius: 0 0 15px 15px;

        .photo-item-actions {
          gap: 30px;

          svg {
            zoom: 0.8;
          }
        }

        .photo-item-description {
          font-weight: 400;
          font-size: 15px;
          line-height: 20px;
        }
      }
    }
  }
}

@media (max-width: 700px) {
  .gallery {
    column-gap: 10px;

    .card {
      margin-bottom: 5px;
      border-radius: 10px;

      img {
        border-radius: 10px;
        min-height: 125px;

      }

      .photo-item-info {
        gap: 8px;
        border-radius: 0 0 10px 10px;

        .photo-item-description {
          font-weight: 400;
          font-size: 13px;
          line-height: 17px;
        }
      }
    }
  }
}

@media (max-width: 500px) {
  .gallery {
    .card {
      img {
        min-height: 110px;
      }

      .photo-item-info {
        .photo-item-description {
          -webkit-line-clamp: 3;
        }
      }
    }
  }
}