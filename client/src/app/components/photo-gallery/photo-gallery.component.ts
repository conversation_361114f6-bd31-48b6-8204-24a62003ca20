import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule, NgOptimizedImage } from '@angular/common';
import { environment } from "@/env/environment";

export class PhotoActionIcon {
  order?: number;
  action: (item: any) => void = () => { };
}

export class ActionsSettings {
  viewIcon?: PhotoActionIcon;
  favoritIcon?: PhotoActionIcon;
  shareIcon?: PhotoActionIcon;
  deleteIcon?: PhotoActionIcon;
  likeIcon?: PhotoActionIcon;
}

@Component({
  selector: 'app-photo-gallery',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage],
  templateUrl: './photo-gallery.component.html',
  styleUrl: './photo-gallery.component.scss'
})
export class PhotoGalleryComponent {
  @Input() images: any[] = [];
  @Input() customPhotoActionsClasses: string = '';
  @Input() actionsSettings: ActionsSettings = this.getDefaultActions();
  @Input() favourites: any = [];
  @Output() onPhotoClick: EventEmitter<any> = new EventEmitter()

  protected readonly environment = environment;

  getBackgroundImageUrl(imageName: string | undefined): string {
    if (!imageName) return '';
    return `${environment.serverUrl}/upload/${imageName}`;
  }

  defaultFunction(item: any): void {
    console.log('Default action triggered with item:', item);
  }


  private getDefaultActions(): ActionsSettings {
    return {
      favoritIcon: {
        action: (item: any) => this.defaultFunction(item)
      },
      shareIcon: {
        action: (item: any) => this.defaultFunction(item)
      },
      likeIcon: {
        action: (item: any) => this.defaultFunction(item)
      }
    };
  }

  inFavourites(id: number): boolean {
    return this.favourites.includes(id)
  }

}
